import { Tabs } from "@/components/ui/Tabs";
import { useFolderStore } from "@/stores/useFolderStore";

export const NoteTabs = () => {
  const folderStore = useFolderStore();
  return (
    <Tabs className="w-full" defaultValue={folderStore.activeNote?.id}>
      <Tabs.List>
        {folderStore.recentNotes.map((note) => (
          <Tabs.Trigger
            key={note.id}
            value={note.id}
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            onClick={() => folderStore.setActiveNote(note.id)}
            onAuxClick={(e) => {
              if (e.button === 1) {
                folderStore.closeNote(note.id);
              }
            }}
          >
            {note.title || "Untitled"}
          </Tabs.Trigger>
        ))}
      </Tabs.List>
    </Tabs>
  );
};
